#!/usr/bin/env python3
"""
Test enhanced detailed logging system
"""

import requests
import json
import time

def test_detailed_logging():
    """Test the enhanced logging with a short message"""
    
    print("🔍 Testing Enhanced Detailed Logging System")
    print("=" * 50)
    print("📝 This will show you exactly what logs you'll see")
    print("⚠️  Watch your server terminal for detailed logs!")
    print()
    
    # Short test message to see all the logging features
    test_message = "Hi! Quick test of the enhanced logging system. You'll see detailed logs of every action including typing simulation, story views, and timing!"
    
    data = {
        "username": "Chirondalonda",
        "message": test_message,
        "human_behaviors": {
            "typing_simulation": True,
            "random_delays": True,
            "pre_dm_activities": {
                "scroll_feed": True,           # Will show feed posts viewed
                "view_stories": True,          # Will show whose stories were watched
                "visit_target_profile": True,  # Will show profile visit details
                "scroll_explore": False        # Skip explore for this test
            },
            "post_dm_activities": {
                "scroll_feed": False,          
                "view_stories": True,          # Will show post-DM story viewing
                "random_profile_visit": False  
            },
            "timing": {
                "delay_before_dm": [5, 10],    # Short delays for testing
                "delay_after_dm": [15, 30],    # Short cooldown for testing
                "typing_speed": [60, 100]      # Faster typing for testing
            }
        }
    }
    
    print(f"📤 Sending test message...")
    print(f"📝 Message: \"{test_message[:60]}...\"")
    print(f"🎛️ Human behaviors: Full simulation enabled")
    print()
    print("👀 WATCH YOUR SERVER TERMINAL NOW!")
    print("   You'll see detailed logs of:")
    print("   🔄 Feed scrolling with specific posts viewed")
    print("   📱 Stories watched with usernames and timing")
    print("   👤 Profile visit with follower counts")
    print("   ⌨️ Typing simulation with real-time progress")
    print("   ⏱️ All delays and timing information")
    print("   📊 Session statistics and summaries")
    print()
    
    start_time = time.time()
    
    try:
        response = requests.post(
            'http://localhost:5000/send-dm',
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=180  # 3 minutes for full simulation
        )
        
        end_time = time.time()
        api_response_time = end_time - start_time
        
        print(f"✅ API Response received in {api_response_time:.1f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        result = response.json()
        
        if result.get('success'):
            print(f"🎉 Message sent successfully!")
            print(f"📧 DM ID: {result.get('dm_id')}")
            print(f"⏱️ Total session time: {result.get('session_duration_seconds', 'N/A')} seconds")
            print(f"🤖 Human behaviors applied: {bool(result.get('human_behaviors_applied'))}")
        else:
            print(f"❌ Error: {result.get('error')}")
        
        print("\n" + "="*50)
        print("📋 WHAT YOU SHOULD HAVE SEEN IN SERVER LOGS:")
        print("🎯 Session start with message preview")
        print("🔄 Feed scrolling with specific posts viewed")
        print("📱 Story viewing with usernames and durations")
        print("👤 Profile visit with follower/following counts")
        print("⏱️ Pre-DM delay with reason")
        print("⌨️ Typing simulation with:")
        print("   📝 Real-time typing progress")
        print("   ⏸️ Thinking pauses with durations")
        print("   📖 Re-reading pauses")
        print("   ✅ Final review time")
        print("   📊 Actual vs target typing speed")
        print("📤 Message sending confirmation")
        print("📱 Post-DM story viewing")
        print("⏱️ Post-DM cooldown with reason")
        print("🎉 Session summary with total time")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Enhanced Logging Test")
    print("This will demonstrate all the detailed logs you'll get")
    print()
    
    # Check if server is running
    try:
        status_response = requests.get('http://localhost:5000/health', timeout=5)
        if status_response.status_code != 200:
            print("❌ Server not responding!")
            exit(1)
    except:
        print("❌ Server not running! Start it with: py dm_server.py")
        exit(1)
    
    success = test_detailed_logging()
    
    if success:
        print("\n🎉 Enhanced logging test completed!")
        print("📊 You now have full visibility into every action")
        print("🔍 Perfect for monitoring your cold outreach campaigns")
    else:
        print("\n❌ Test failed - check server logs")
