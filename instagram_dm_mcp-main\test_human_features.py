#!/usr/bin/env python3
"""
Test script for enhanced human-like Instagram DM features
Tests the running server without causing new logins
"""

import requests
import json
import time

def test_basic_dm():
    """Test basic DM (no human behaviors)"""
    print("🧪 Testing Basic DM (Fast Mode)")
    
    data = {
        "username": "Chi<PERSON><PERSON>onda",
        "message": "Quick test message - no human simulation"
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/send-dm',
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_minimal_human():
    """Test minimal human behaviors"""
    print("\n🤖 Testing Minimal Human Behaviors")
    
    data = {
        "username": "Chirondalonda",
        "message": "Test with minimal human simulation - typing and profile visit",
        "human_behaviors": "minimal"
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/send-dm',
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=60
        )
        
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_custom_behaviors():
    """Test custom human behaviors with individual controls"""
    print("\n🎛️ Testing Custom Human Behaviors")
    
    data = {
        "username": "Chirondalonda",
        "message": "Custom test - typing simulation, feed scroll, and profile visit only",
        "human_behaviors": {
            "typing_simulation": True,
            "random_delays": True,
            "pre_dm_activities": {
                "scroll_feed": True,           # Enable feed scrolling
                "view_stories": False,         # Skip stories
                "visit_target_profile": True,  # Visit their profile
                "scroll_explore": False        # Skip explore
            },
            "post_dm_activities": {
                "scroll_feed": False,          # No post-DM feed scroll
                "view_stories": False,         # No post-DM stories
                "random_profile_visit": False  # No random profiles
            },
            "timing": {
                "delay_before_dm": [3, 8],     # 3-8 seconds before
                "delay_after_dm": [10, 20],    # 10-20 seconds after (shorter for testing)
                "typing_speed": [50, 90]       # 50-90 chars per minute
            }
        }
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/send-dm',
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=120
        )
        
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_server_status():
    """Check if server is running and healthy"""
    print("🔍 Checking Server Status")
    
    try:
        response = requests.get('http://localhost:5000/status', timeout=10)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
        return response.status_code == 200 and result.get('instagram_connected', False)
    except Exception as e:
        print(f"❌ Status check failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Enhanced Instagram DM Server")
    print("=" * 50)
    print("⚠️  Using existing server session - no new logins!")
    print()
    
    # Check server status first
    if not test_server_status():
        print("❌ Server not running or Instagram not connected!")
        print("Please start the server first: py dm_server.py")
        exit(1)
    
    print("\n" + "="*50)
    
    # Test 1: Basic DM (fast)
    success1 = test_basic_dm()
    
    print("\n" + "="*50)
    
    # Test 2: Minimal human behaviors
    success2 = test_minimal_human()
    
    print("\n" + "="*50)
    
    # Test 3: Custom behaviors
    success3 = test_custom_behaviors()
    
    print("\n" + "="*50)
    print("📊 Test Results:")
    print(f"Basic DM: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Minimal Human: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"Custom Behaviors: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if all([success1, success2, success3]):
        print("\n🎉 All tests passed! Human behaviors are working!")
    else:
        print("\n⚠️ Some tests failed. Check the server logs.")
    
    print("\n📝 n8n Integration Examples:")
    print("Fast mode: {\"username\": \"user\", \"message\": \"hello\"}")
    print("Minimal: {\"username\": \"user\", \"message\": \"hello\", \"human_behaviors\": \"minimal\"}")
    print("Custom: Use the custom JSON structure shown above")
