#!/usr/bin/env python3
"""
Test real cold outreach messages with human typing simulation
"""

import requests
import json
import time

def send_message_with_human_typing(message_num, message_text):
    """Send message with realistic human typing speed"""
    
    # Calculate typing speed for 80-100 seconds
    message_length = len(message_text)
    target_time_range = [80, 100]  # seconds
    
    # Calculate characters per minute for this timing
    chars_per_minute_min = (message_length / target_time_range[1]) * 60
    chars_per_minute_max = (message_length / target_time_range[0]) * 60
    
    print(f"\n📝 Message {message_num} Details:")
    print(f"   Length: {message_length} characters")
    print(f"   Target typing time: {target_time_range[0]}-{target_time_range[1]} seconds")
    print(f"   Calculated typing speed: {chars_per_minute_min:.1f}-{chars_per_minute_max:.1f} chars/min")
    
    data = {
        "username": "Chirondalonda",
        "message": message_text,
        "human_behaviors": {
            "typing_simulation": True,
            "random_delays": True,
            "pre_dm_activities": {
                "scroll_feed": True,           # Browse feed before messaging
                "view_stories": False,         # Skip stories for now
                "visit_target_profile": True,  # Visit their profile first
                "scroll_explore": False        # Skip explore
            },
            "post_dm_activities": {
                "scroll_feed": False,          # No post-DM scrolling
                "view_stories": True,          # Check stories after
                "random_profile_visit": False  # No random profiles
            },
            "timing": {
                "delay_before_dm": [10, 20],   # 10-20 seconds before typing
                "delay_after_dm": [60, 120],   # 1-2 minutes after sending
                "typing_speed": [chars_per_minute_min, chars_per_minute_max]  # Custom speed
            }
        }
    }
    
    print(f"🚀 Sending message {message_num} with human simulation...")
    start_time = time.time()
    
    try:
        response = requests.post(
            'http://localhost:5000/send-dm',
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=300  # 5 minutes timeout for long typing simulation
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"✅ Message {message_num} completed!")
        print(f"⏱️ Total time taken: {total_time:.1f} seconds")
        print(f"📊 Status: {response.status_code}")
        
        result = response.json()
        if result.get('success'):
            print(f"📧 DM ID: {result.get('dm_id')}")
            print(f"🎯 Target: {result.get('target_username')}")
        else:
            print(f"❌ Error: {result.get('error')}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Message {message_num} failed: {e}")
        return False

def main():
    print("🎯 Testing Real Cold Outreach Messages")
    print("=" * 50)
    print("👤 Target: Chirondalonda")
    print("⌨️ Typing Speed: 80-100 seconds per message")
    print("🤖 Human Behaviors: Enabled")
    print()
    
    # Message 1
    message1 = """Hello CC Cabot this is Hitesh from Vadodara   

I'll keep it short and crisp  

What are your thoughts about a website where brands and audiences connect with you professionally, present all kinds of content creation including different types of streams you produce, present testimonials, it'll boost trust, sales and your brand name, you can also generate a side INCOME from there  

The site will serve as your dedicated streamer portfolio hub beyond Instagram  

Just lemme know if you're interested or wanna hear the website's benefits and features first!"""

    # Message 2  
    message2 = """Hello hitesh this is Hitesh from Vadodara   

I'll keep it short and crisp  

What are your thoughts about a website where brands and audiences connect with you professionally, present all kinds of content creation including different types of streams you produce, present testimonials, it'll boost trust, sales and your brand name, you can also generate a side INCOME from there  

The site will serve as your dedicated streamer portfolio hub beyond Instagram  

Just lemme know if you're interested or wanna hear the website's benefits and features first!"""
    
    # Send Message 1
    print("🔥 SENDING MESSAGE 1")
    print("-" * 30)
    success1 = send_message_with_human_typing(1, message1)
    
    if success1:
        print("\n⏳ Waiting 3 minutes before sending message 2...")
        time.sleep(180)  # 3 minutes gap between messages
    
    # Send Message 2
    print("\n🔥 SENDING MESSAGE 2") 
    print("-" * 30)
    success2 = send_message_with_human_typing(2, message2)
    
    # Results
    print("\n" + "=" * 50)
    print("📊 FINAL RESULTS:")
    print(f"Message 1: {'✅ SUCCESS' if success1 else '❌ FAILED'}")
    print(f"Message 2: {'✅ SUCCESS' if success2 else '❌ FAILED'}")
    
    if success1 and success2:
        print("\n🎉 Both messages sent successfully with human simulation!")
        print("🤖 The recipient will see natural typing patterns")
        print("⏱️ Realistic delays and activities were performed")
    else:
        print("\n⚠️ Some messages failed. Check server logs.")

if __name__ == "__main__":
    main()
