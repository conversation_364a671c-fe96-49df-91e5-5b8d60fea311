#!/usr/bin/env python3
"""
Instagram DM API Server for Cold Outreach Automation
Accepts JSON requests with username and message, sends Instagram DMs
Maintains persistent login session to avoid repeated logins
"""

import os
import sys
from dotenv import load_dotenv
from instagrapi import Client
import logging
from flask import Flask, request, jsonify
import json
from datetime import datetime
import time

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global Instagram client instance (persistent session)
instagram_client = None
login_time = None

app = Flask(__name__)

def initialize_instagram_client():
    """Initialize and login to Instagram once"""
    global instagram_client, login_time

    # Get credentials from environment variables
    username = os.getenv("INSTAGRAM_USERNAME")
    password = os.getenv("INSTAGRAM_PASSWORD")

    if not username or not password:
        logger.error("❌ Instagram credentials not found in .env file")
        return False

    try:
        logger.info(f"📱 Initializing Instagram client for account: {username}")
        instagram_client = Client()

        logger.info("🔐 Logging into Instagram...")
        instagram_client.login(username, password)
        login_time = datetime.now()

        logger.info("✅ Successfully logged in to Instagram")
        logger.info(f"🕐 Login time: {login_time}")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to login to Instagram: {str(e)}")
        instagram_client = None
        return False

def check_session_health():
    """Check if Instagram session is still valid"""
    global instagram_client, login_time

    if not instagram_client:
        return False

    # Check if session is older than 2 hours (optional refresh)
    if login_time and (datetime.now() - login_time).seconds > 7200:
        logger.info("🔄 Session is old, might need refresh")

    try:
        # Test session by getting account info
        instagram_client.account_info()
        return True
    except Exception as e:
        logger.warning(f"⚠️ Session health check failed: {str(e)}")
        return False

@app.route('/send-dm', methods=['POST'])
def send_dm():
    """
    API endpoint to send Instagram DM
    Expected JSON format:
    {
        "username": "target_username",
        "message": "personalized message content"
    }
    """
    global instagram_client

    try:
        # Check if client is initialized
        if not instagram_client:
            logger.warning("🔄 Instagram client not initialized, initializing now...")
            if not initialize_instagram_client():
                return jsonify({
                    "success": False,
                    "error": "Failed to initialize Instagram client",
                    "timestamp": datetime.now().isoformat()
                }), 500

        # Check session health
        if not check_session_health():
            logger.warning("🔄 Session unhealthy, re-initializing...")
            if not initialize_instagram_client():
                return jsonify({
                    "success": False,
                    "error": "Failed to re-initialize Instagram client",
                    "timestamp": datetime.now().isoformat()
                }), 500

        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided",
                "timestamp": datetime.now().isoformat()
            }), 400

        target_username = data.get('username')
        message_text = data.get('message')

        if not target_username or not message_text:
            return jsonify({
                "success": False,
                "error": "Both 'username' and 'message' fields are required",
                "timestamp": datetime.now().isoformat()
            }), 400

        logger.info(f"📨 Processing DM request for user: {target_username}")

        # Look up user
        try:
            user_id = instagram_client.user_id_from_username(target_username)

            if not user_id:
                return jsonify({
                    "success": False,
                    "error": f"User '{target_username}' not found",
                    "timestamp": datetime.now().isoformat()
                }), 404

            logger.info(f"✅ Found user {target_username} with ID: {user_id}")

        except Exception as e:
            logger.error(f"❌ Error looking up user {target_username}: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"Error looking up user: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }), 500

        # Send message
        try:
            logger.info(f"📤 Sending message to {target_username}")
            dm = instagram_client.direct_send(message_text, [user_id])

            if dm:
                dm_id = getattr(dm, 'id', 'N/A')
                logger.info(f"✅ Message sent successfully! DM ID: {dm_id}")

                return jsonify({
                    "success": True,
                    "message": "DM sent successfully",
                    "target_username": target_username,
                    "target_user_id": str(user_id),
                    "dm_id": str(dm_id),
                    "timestamp": datetime.now().isoformat()
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": "Failed to send message - no response from Instagram",
                    "timestamp": datetime.now().isoformat()
                }), 500

        except Exception as e:
            logger.error(f"❌ Error sending message: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"Error sending message: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }), 500

    except Exception as e:
        logger.error(f"❌ Unexpected error in send_dm: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/status', methods=['GET'])
def status():
    """Check API and Instagram session status"""
    global instagram_client, login_time

    session_healthy = check_session_health()

    return jsonify({
        "api_status": "running",
        "instagram_logged_in": instagram_client is not None,
        "session_healthy": session_healthy,
        "login_time": login_time.isoformat() if login_time else None,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/health', methods=['GET'])
def health():
    """Simple health check endpoint"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

if __name__ == "__main__":
    print("🚀 Instagram DM API Server for Cold Outreach")
    print("=" * 50)

    # Initialize Instagram client on startup
    if initialize_instagram_client():
        print("✅ Instagram client initialized successfully")
        print("\n📡 Starting API server...")
        print("🔗 Endpoints:")
        print("   POST /send-dm - Send Instagram DM")
        print("   GET  /status  - Check status")
        print("   GET  /health  - Health check")
        print("\n📝 Example request:")
        print('   curl -X POST http://localhost:5000/send-dm \\')
        print('        -H "Content-Type: application/json" \\')
        print('        -d \'{"username": "target_user", "message": "Hello!"}\'')
        print("\n🌐 Server running on http://localhost:5000")

        # Run Flask app
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        print("💥 Failed to initialize Instagram client!")
        sys.exit(1)
