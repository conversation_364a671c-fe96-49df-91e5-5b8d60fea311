#!/usr/bin/env python3
"""
Test script to send an Instagram DM message
"""

import os
import sys
from dotenv import load_dotenv
from instagrapi import Client
import logging

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_send_message():
    """Test sending an Instagram DM"""
    
    # Get credentials from environment variables
    username = os.getenv("INSTAGRAM_USERNAME")
    password = os.getenv("INSTAGRAM_PASSWORD")
    
    if not username or not password:
        print("❌ Instagram credentials not found in .env file")
        return False
    
    print(f"📱 Testing Instagram DM with account: {username}")
    
    # Create client and login
    client = Client()
    
    try:
        print("🔐 Logging into Instagram...")
        client.login(username, password)
        print("✅ Successfully logged in to Instagram")
    except Exception as e:
        print(f"❌ Failed to login to Instagram: {str(e)}")
        return False
    
    # Test message details
    target_username = "Chirondalonda"
    message_text = "hello"
    
    try:
        print(f"🔍 Looking up user: {target_username}")
        user_id = client.user_id_from_username(target_username)
        
        if not user_id:
            print(f"❌ User '{target_username}' not found")
            return False
        
        print(f"✅ Found user {target_username} with ID: {user_id}")
        
        print(f"📤 Sending message: '{message_text}'")
        dm = client.direct_send(message_text, [user_id])
        
        if dm:
            print("✅ Message sent successfully!")
            print(f"📧 Direct message ID: {getattr(dm, 'id', 'N/A')}")
            return True
        else:
            print("❌ Failed to send message - no response from Instagram")
            return False
            
    except Exception as e:
        print(f"❌ Error sending message: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Instagram DM Test Script")
    print("=" * 40)
    
    success = test_send_message()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
        sys.exit(1)
