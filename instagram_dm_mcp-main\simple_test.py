#!/usr/bin/env python3
"""
Simple test to send a DM using the Instagram client directly
"""

import os
import sys
from dotenv import load_dotenv
from instagrapi import Client
import json

# Load environment variables
load_dotenv()

def send_dm_direct(username, message):
    """Send DM directly using Instagram client"""
    
    # Get credentials
    ig_username = os.getenv("INSTAGRAM_USERNAME")
    ig_password = os.getenv("INSTAGRAM_PASSWORD")
    
    if not ig_username or not ig_password:
        return {"success": False, "error": "Credentials not found"}
    
    try:
        # Create client and login
        client = Client()
        client.login(ig_username, ig_password)
        
        # Get user ID
        user_id = client.user_id_from_username(username)
        if not user_id:
            return {"success": False, "error": f"User {username} not found"}
        
        # Send message
        dm = client.direct_send(message, [user_id])
        
        if dm:
            return {
                "success": True,
                "message": "DM sent successfully",
                "target_username": username,
                "target_user_id": str(user_id),
                "dm_id": str(getattr(dm, 'id', 'N/A'))
            }
        else:
            return {"success": False, "error": "Failed to send message"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("🧪 Simple Instagram DM Test")
    print("=" * 30)
    
    # Test data (simulating what would come from n8n)
    test_data = {
        "username": "Chirondalonda",
        "message": "Hello! This is a test message from your automated cold outreach system. Hope you're doing well! 😊"
    }
    
    print(f"📤 Sending DM to: {test_data['username']}")
    print(f"💬 Message: {test_data['message']}")
    
    result = send_dm_direct(test_data['username'], test_data['message'])
    
    print("\n📋 Result:")
    print(json.dumps(result, indent=2))
    
    if result['success']:
        print("\n✅ SUCCESS: Message sent successfully!")
    else:
        print(f"\n❌ FAILED: {result['error']}")
