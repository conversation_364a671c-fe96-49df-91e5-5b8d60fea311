#!/usr/bin/env python3
"""
Instagram DM API Server for n8n Cold Outreach Integration
"""

import os
import sys
from dotenv import load_dotenv
from instagrapi import Client
from flask import Flask, request, jsonify
from datetime import datetime
import logging
import time
import random

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global Instagram client
instagram_client = None

app = Flask(__name__)

def get_instagram_client():
    """Get or create Instagram client with persistent session"""
    global instagram_client

    if instagram_client is None:
        # Get credentials
        username = os.getenv("INSTAGRAM_USERNAME")
        password = os.getenv("INSTAGRAM_PASSWORD")

        if not username or not password:
            raise Exception("Instagram credentials not found in .env file")

        # Create and login
        instagram_client = Client()
        instagram_client.login(username, password)
        logger.info(f"✅ Logged into Instagram as {username}")

    return instagram_client

def simulate_human_activity(client, activities_config):
    """Simulate human-like activities before/after sending DMs"""
    try:
        if activities_config.get('scroll_feed', False):
            logger.info("🔄 Scrolling home feed...")
            client.feed_timeline()
            time.sleep(random.uniform(2, 5))

        if activities_config.get('view_stories', False):
            logger.info("👀 Viewing stories...")
            try:
                client.feed_reels_tray()
                time.sleep(random.uniform(3, 7))
            except:
                pass  # Stories might not be available

        if activities_config.get('scroll_explore', False):
            logger.info("🔍 Browsing explore page...")
            try:
                client.feed_popular()
                time.sleep(random.uniform(2, 6))
            except:
                pass  # Explore might not be available

    except Exception as e:
        logger.warning(f"⚠️ Activity simulation warning: {str(e)}")

def simulate_typing_and_send(client, user_id, message, typing_config):
    """Simulate human typing and send message"""
    try:
        # Get typing speed (characters per minute)
        typing_speed = random.uniform(
            typing_config.get('typing_speed', [40, 80])[0],
            typing_config.get('typing_speed', [40, 80])[1]
        )

        # Calculate delay per character (in seconds)
        chars_per_second = typing_speed / 60

        if typing_config.get('typing_simulation', False):
            logger.info("⌨️ Simulating typing...")

            # Break message into chunks for realistic typing
            words = message.split()
            typed_message = ""

            for i, word in enumerate(words):
                # Add word
                typed_message += word

                # Simulate typing time for this word
                typing_time = len(word) / chars_per_second
                time.sleep(typing_time)

                # Add space and pause between words
                if i < len(words) - 1:
                    typed_message += " "
                    time.sleep(random.uniform(0.1, 0.5))  # Pause between words

                # Occasional longer pauses (like thinking)
                if random.random() < 0.1:  # 10% chance
                    time.sleep(random.uniform(1, 3))

        # Final pause before sending (like reviewing message)
        time.sleep(random.uniform(1, 3))

        # Send the message
        dm = client.direct_send(message, [user_id])
        return dm

    except Exception as e:
        logger.error(f"❌ Typing simulation error: {str(e)}")
        # Fallback to direct send
        return client.direct_send(message, [user_id])

@app.route('/send-dm', methods=['POST'])
def send_dm():
    """
    Send Instagram DM with optional human-like behaviors
    Expected JSON: {
        "username": "target_user",
        "message": "your message",
        "human_behaviors": {
            "typing_simulation": true,
            "random_delays": true,
            "pre_dm_activities": {
                "scroll_feed": true,
                "view_stories": false,
                "visit_target_profile": true,
                "scroll_explore": false
            },
            "post_dm_activities": {
                "scroll_feed": false,
                "view_stories": true,
                "random_profile_visit": false
            },
            "timing": {
                "delay_before_dm": [5, 15],
                "delay_after_dm": [30, 120],
                "typing_speed": [40, 80]
            }
        }
    }
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No JSON data provided"}), 400

        username = data.get('username')
        message = data.get('message')
        human_behaviors = data.get('human_behaviors', {})

        if not username or not message:
            return jsonify({
                "success": False,
                "error": "Both 'username' and 'message' are required"
            }), 400

        logger.info(f"📤 Processing DM request for {username}")

        # Handle preset behaviors
        if human_behaviors == "minimal":
            human_behaviors = {
                "typing_simulation": True,
                "random_delays": True,
                "pre_dm_activities": {"visit_target_profile": True},
                "timing": {"delay_before_dm": [2, 5], "typing_speed": [60, 100]}
            }
        elif human_behaviors == "full":
            human_behaviors = {
                "typing_simulation": True,
                "random_delays": True,
                "pre_dm_activities": {
                    "scroll_feed": True,
                    "view_stories": True,
                    "visit_target_profile": True,
                    "scroll_explore": True
                },
                "post_dm_activities": {
                    "scroll_feed": True,
                    "view_stories": True
                },
                "timing": {
                    "delay_before_dm": [10, 30],
                    "delay_after_dm": [60, 180],
                    "typing_speed": [40, 80]
                }
            }

        # Get Instagram client
        client = get_instagram_client()

        # Pre-DM activities
        pre_activities = human_behaviors.get('pre_dm_activities', {})
        if pre_activities:
            logger.info("🎭 Performing pre-DM human activities...")
            simulate_human_activity(client, pre_activities)

        # Visit target profile if requested
        if pre_activities.get('visit_target_profile', False):
            logger.info(f"👤 Visiting {username}'s profile...")
            try:
                client.user_info_by_username(username)
                time.sleep(random.uniform(2, 5))
            except:
                pass

        # Get user ID
        user_id = client.user_id_from_username(username)
        if not user_id:
            return jsonify({
                "success": False,
                "error": f"User '{username}' not found"
            }), 404

        # Pre-DM delay
        timing = human_behaviors.get('timing', {})
        if human_behaviors.get('random_delays', False):
            delay_range = timing.get('delay_before_dm', [2, 8])
            delay = random.uniform(delay_range[0], delay_range[1])
            logger.info(f"⏱️ Waiting {delay:.1f} seconds before sending...")
            time.sleep(delay)

        # Send message with typing simulation
        logger.info(f"📤 Sending DM to {username}")
        if human_behaviors.get('typing_simulation', False):
            dm = simulate_typing_and_send(client, user_id, message, timing)
        else:
            dm = client.direct_send(message, [user_id])

        if dm:
            # Post-DM activities
            post_activities = human_behaviors.get('post_dm_activities', {})
            if post_activities:
                logger.info("🎭 Performing post-DM human activities...")
                simulate_human_activity(client, post_activities)

            # Post-DM delay
            if human_behaviors.get('random_delays', False):
                delay_range = timing.get('delay_after_dm', [30, 120])
                delay = random.uniform(delay_range[0], delay_range[1])
                logger.info(f"⏱️ Waiting {delay:.1f} seconds after sending...")
                time.sleep(delay)

            result = {
                "success": True,
                "message": "DM sent successfully",
                "target_username": username,
                "target_user_id": str(user_id),
                "dm_id": str(getattr(dm, 'id', 'N/A')),
                "human_behaviors_applied": human_behaviors,
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"✅ DM sent to {username}")
            return jsonify(result), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to send message"
            }), 500

    except Exception as e:
        logger.error(f"❌ Error: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/status', methods=['GET'])
def status():
    """Check server status"""
    try:
        client = get_instagram_client()
        # Test session
        client.account_info()
        
        return jsonify({
            "status": "healthy",
            "instagram_connected": True,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "instagram_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@app.route('/health', methods=['GET'])
def health():
    """Simple health check"""
    return jsonify({"status": "ok", "timestamp": datetime.now().isoformat()})

if __name__ == "__main__":
    print("🚀 Instagram DM Server for n8n Integration")
    print("=" * 45)
    print("🔗 Endpoints:")
    print("   POST /send-dm - Send Instagram DM")
    print("   GET  /status  - Check status")
    print("   GET  /health  - Health check")
    print("\n📝 Example n8n HTTP Request:")
    print("   URL: http://localhost:5000/send-dm")
    print("   Method: POST")
    print("   Headers: Content-Type: application/json")
    print('   Body: {"username": "target_user", "message": "Hello!"}')
    print("\n🌐 Starting server on http://localhost:5000")
    
    try:
        # Test Instagram connection on startup
        get_instagram_client()
        print("✅ Instagram connection successful")
        
        # Start Flask server
        app.run(host='127.0.0.1', port=5000, debug=False)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
