#!/usr/bin/env python3
"""
Instagram DM API Server for n8n Cold Outreach Integration
"""

import os
import sys
from dotenv import load_dotenv
from instagrapi import Client
from flask import Flask, request, jsonify
from datetime import datetime
import logging
import time
import random

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global Instagram client
instagram_client = None

app = Flask(__name__)

def get_instagram_client():
    """Get or create Instagram client with persistent session"""
    global instagram_client

    if instagram_client is None:
        # Get credentials
        username = os.getenv("INSTAGRAM_USERNAME")
        password = os.getenv("INSTAGRAM_PASSWORD")

        if not username or not password:
            raise Exception("Instagram credentials not found in .env file")

        # Create and login
        instagram_client = Client()
        instagram_client.login(username, password)
        logger.info(f"✅ Logged into Instagram as {username}")

    return instagram_client

def simulate_human_activity(client, activities_config, activity_type="pre-dm"):
    """Simulate human-like activities with detailed logging"""
    total_activity_time = 0
    start_time = time.time()

    logger.info(f"🎭 Starting {activity_type} human activities...")

    try:
        if activities_config.get('scroll_feed', False):
            logger.info("🔄 Scrolling home feed...")
            feed_start = time.time()

            try:
                # Get some feed posts
                feed_posts = client.feed_timeline()

                # Simulate viewing 2-4 posts
                posts_to_view = random.randint(2, 4)
                for i in range(min(posts_to_view, len(feed_posts))):
                    if i < len(feed_posts):
                        post = feed_posts[i]
                        username = getattr(post.user, 'username', f'user_{i+1}')
                        view_time = random.uniform(1.5, 3.0)
                        logger.info(f"   👀 Viewed post by @{username} ({view_time:.1f}s)")
                        time.sleep(view_time)

            except Exception as e:
                # Fallback to simple delay
                logger.info("   📱 Browsing feed content...")
                time.sleep(random.uniform(3, 6))

            feed_time = time.time() - feed_start
            total_activity_time += feed_time
            logger.info(f"   📊 Total feed browsing time: {feed_time:.1f} seconds")

        if activities_config.get('view_stories', False):
            logger.info("👀 Viewing stories...")
            stories_start = time.time()

            try:
                # Get stories tray
                stories_tray = client.feed_reels_tray()

                # Simulate viewing 1-3 stories
                stories_to_view = random.randint(1, 3)
                viewed_stories = 0

                for story in stories_tray[:stories_to_view]:
                    try:
                        username = getattr(story.user, 'username', f'story_user_{viewed_stories+1}')
                        view_time = random.uniform(2.0, 4.5)
                        logger.info(f"   📱 Watched @{username}'s story ({view_time:.1f}s)")
                        time.sleep(view_time)
                        viewed_stories += 1
                    except:
                        continue

                if viewed_stories == 0:
                    logger.info("   📱 Browsed stories section...")
                    time.sleep(random.uniform(3, 6))

            except Exception as e:
                logger.info("   📱 Browsed stories section...")
                time.sleep(random.uniform(3, 6))

            stories_time = time.time() - stories_start
            total_activity_time += stories_time
            logger.info(f"   📊 Total story viewing time: {stories_time:.1f} seconds")

        if activities_config.get('scroll_explore', False):
            logger.info("🔍 Browsing explore page...")
            explore_start = time.time()

            try:
                # Get explore content
                client.feed_popular()

                # Simulate browsing explore
                browse_time = random.uniform(2, 5)
                logger.info(f"   🎯 Browsed trending content ({browse_time:.1f}s)")
                time.sleep(browse_time)

            except Exception as e:
                logger.info("   🎯 Browsed explore content...")
                time.sleep(random.uniform(2, 5))

            explore_time = time.time() - explore_start
            total_activity_time += explore_time
            logger.info(f"   📊 Total explore time: {explore_time:.1f} seconds")

    except Exception as e:
        logger.warning(f"⚠️ Activity simulation warning: {str(e)}")

    total_time = time.time() - start_time
    logger.info(f"✅ {activity_type.title()} activities completed in {total_time:.1f} seconds")

def simulate_typing_and_send(client, user_id, message, typing_config):
    """Simulate human typing with detailed logging"""
    typing_start_time = time.time()

    try:
        # Get typing speed (characters per minute)
        typing_speed = random.uniform(
            typing_config.get('typing_speed', [40, 80])[0],
            typing_config.get('typing_speed', [40, 80])[1]
        )

        logger.info(f"⌨️ Starting typing simulation...")
        logger.info(f"   📊 Message length: {len(message)} characters")
        logger.info(f"   🎯 Typing speed: {typing_speed:.1f} chars/min")

        # Calculate delay per character (in seconds)
        chars_per_second = typing_speed / 60
        estimated_time = len(message) / chars_per_second
        logger.info(f"   ⏱️ Estimated typing time: {estimated_time:.1f} seconds")

        if typing_config.get('typing_simulation', False):
            # Break message into sentences for more realistic logging
            sentences = message.replace('\n\n', '|PARAGRAPH|').split('.')
            typed_chars = 0

            for i, sentence in enumerate(sentences):
                if not sentence.strip():
                    continue

                # Handle paragraph breaks
                if '|PARAGRAPH|' in sentence:
                    parts = sentence.split('|PARAGRAPH|')
                    for part in parts:
                        if part.strip():
                            typing_chunk_time = len(part) / chars_per_second
                            logger.info(f"   📝 Typing: \"{part.strip()[:30]}...\" ({typing_chunk_time:.1f}s)")
                            time.sleep(typing_chunk_time)
                            typed_chars += len(part)

                        # Paragraph pause
                        if '|PARAGRAPH|' in sentence:
                            pause_time = random.uniform(1.5, 3.0)
                            logger.info(f"   ⏸️ Paragraph break pause: {pause_time:.1f}s")
                            time.sleep(pause_time)
                else:
                    # Regular sentence
                    sentence_clean = sentence.strip()
                    if sentence_clean:
                        typing_time = len(sentence_clean) / chars_per_second
                        preview = sentence_clean[:40] + "..." if len(sentence_clean) > 40 else sentence_clean
                        logger.info(f"   📝 Typing: \"{preview}\" ({typing_time:.1f}s)")
                        time.sleep(typing_time)
                        typed_chars += len(sentence_clean)

                        # Sentence pause (thinking/reviewing)
                        if i < len(sentences) - 1 and random.random() < 0.3:  # 30% chance
                            pause_time = random.uniform(0.8, 2.5)
                            logger.info(f"   🤔 Thinking pause: {pause_time:.1f}s")
                            time.sleep(pause_time)

                # Occasional longer pauses (like re-reading)
                if random.random() < 0.15:  # 15% chance
                    pause_time = random.uniform(2, 4)
                    logger.info(f"   📖 Re-reading pause: {pause_time:.1f}s")
                    time.sleep(pause_time)

        # Final review pause before sending
        review_time = random.uniform(2, 5)
        logger.info(f"   ✅ Final review pause: {review_time:.1f}s")
        time.sleep(review_time)

        total_typing_time = time.time() - typing_start_time
        actual_speed = (len(message) / total_typing_time) * 60 if total_typing_time > 0 else 0

        logger.info(f"   📊 Total typing time: {total_typing_time:.1f} seconds")
        logger.info(f"   📊 Actual typing speed: {actual_speed:.1f} chars/min")

        # Send the message
        logger.info("📤 Sending message...")
        dm = client.direct_send(message, [user_id])
        return dm

    except Exception as e:
        logger.error(f"❌ Typing simulation error: {str(e)}")
        # Fallback to direct send
        return client.direct_send(message, [user_id])

@app.route('/send-dm', methods=['POST'])
def send_dm():
    """
    Send Instagram DM with optional human-like behaviors
    Expected JSON: {
        "username": "target_user",
        "message": "your message",
        "human_behaviors": {
            "typing_simulation": true,
            "random_delays": true,
            "pre_dm_activities": {
                "scroll_feed": true,
                "view_stories": false,
                "visit_target_profile": true,
                "scroll_explore": false
            },
            "post_dm_activities": {
                "scroll_feed": false,
                "view_stories": true,
                "random_profile_visit": false
            },
            "timing": {
                "delay_before_dm": [5, 15],
                "delay_after_dm": [30, 120],
                "typing_speed": [40, 80]
            }
        }
    }
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No JSON data provided"}), 400

        username = data.get('username')
        message = data.get('message')
        human_behaviors = data.get('human_behaviors', {})

        if not username or not message:
            return jsonify({
                "success": False,
                "error": "Both 'username' and 'message' are required"
            }), 400

        logger.info(f"📤 Processing DM request for {username}")

        # Handle preset behaviors
        if human_behaviors == "minimal":
            human_behaviors = {
                "typing_simulation": True,
                "random_delays": True,
                "pre_dm_activities": {"visit_target_profile": True},
                "timing": {"delay_before_dm": [2, 5], "typing_speed": [60, 100]}
            }
        elif human_behaviors == "full":
            human_behaviors = {
                "typing_simulation": True,
                "random_delays": True,
                "pre_dm_activities": {
                    "scroll_feed": True,
                    "view_stories": True,
                    "visit_target_profile": True,
                    "scroll_explore": True
                },
                "post_dm_activities": {
                    "scroll_feed": True,
                    "view_stories": True
                },
                "timing": {
                    "delay_before_dm": [10, 30],
                    "delay_after_dm": [60, 180],
                    "typing_speed": [40, 80]
                }
            }

        # Get Instagram client
        client = get_instagram_client()

        # Session start logging
        session_start_time = time.time()
        logger.info(f"🎯 Starting DM session for @{username}")
        logger.info(f"   📝 Message preview: \"{message[:50]}...\"")
        logger.info(f"   🤖 Human behaviors: {human_behaviors != {}}")

        # Pre-DM activities
        pre_activities = human_behaviors.get('pre_dm_activities', {})
        if pre_activities:
            simulate_human_activity(client, pre_activities, "pre-dm")

        # Visit target profile if requested
        if pre_activities.get('visit_target_profile', False):
            logger.info(f"👤 Visiting @{username}'s profile...")
            profile_start = time.time()
            try:
                user_info = client.user_info_by_username(username)
                profile_time = random.uniform(3, 7)
                logger.info(f"   📊 Profile info: {user_info.follower_count} followers, {user_info.following_count} following")
                logger.info(f"   ⏱️ Profile visit time: {profile_time:.1f}s")
                time.sleep(profile_time)
            except Exception as e:
                logger.info(f"   ⏱️ Profile visit time: 4.0s")
                time.sleep(4.0)

        # Get user ID
        user_id = client.user_id_from_username(username)
        if not user_id:
            return jsonify({
                "success": False,
                "error": f"User '{username}' not found"
            }), 404

        # Pre-DM delay
        timing = human_behaviors.get('timing', {})
        if human_behaviors.get('random_delays', False):
            delay_range = timing.get('delay_before_dm', [2, 8])
            delay = random.uniform(delay_range[0], delay_range[1])
            logger.info(f"⏱️ Pre-DM delay: {delay:.1f} seconds (looking natural)")
            time.sleep(delay)

        # Send message with typing simulation
        logger.info(f"📤 Sending DM to {username}")
        if human_behaviors.get('typing_simulation', False):
            dm = simulate_typing_and_send(client, user_id, message, timing)
        else:
            dm = client.direct_send(message, [user_id])

        if dm:
            dm_id = str(getattr(dm, 'id', 'N/A'))
            logger.info(f"✅ Message sent successfully! DM ID: {dm_id}")

            # Post-DM activities
            post_activities = human_behaviors.get('post_dm_activities', {})
            if post_activities:
                simulate_human_activity(client, post_activities, "post-dm")

            # Post-DM delay
            if human_behaviors.get('random_delays', False):
                delay_range = timing.get('delay_after_dm', [30, 120])
                delay = random.uniform(delay_range[0], delay_range[1])
                logger.info(f"⏱️ Post-DM cooldown: {delay:.1f} seconds (avoiding spam detection)")
                time.sleep(delay)

            # Session summary
            total_session_time = time.time() - session_start_time
            logger.info(f"🎉 Session completed! Total time: {total_session_time:.1f} seconds ({total_session_time/60:.1f} minutes)")

            result = {
                "success": True,
                "message": "DM sent successfully",
                "target_username": username,
                "target_user_id": str(user_id),
                "dm_id": dm_id,
                "human_behaviors_applied": human_behaviors,
                "session_duration_seconds": round(total_session_time, 1),
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"📊 Session stats: {total_session_time:.1f}s total, human behaviors: {bool(human_behaviors)}")
            return jsonify(result), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to send message"
            }), 500

    except Exception as e:
        logger.error(f"❌ Error: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/status', methods=['GET'])
def status():
    """Check server status"""
    try:
        client = get_instagram_client()
        # Test session
        client.account_info()
        
        return jsonify({
            "status": "healthy",
            "instagram_connected": True,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "instagram_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@app.route('/health', methods=['GET'])
def health():
    """Simple health check"""
    return jsonify({"status": "ok", "timestamp": datetime.now().isoformat()})

if __name__ == "__main__":
    print("🚀 Instagram DM Server for n8n Integration")
    print("=" * 45)
    print("🔗 Endpoints:")
    print("   POST /send-dm - Send Instagram DM")
    print("   GET  /status  - Check status")
    print("   GET  /health  - Health check")
    print("\n📝 Example n8n HTTP Request:")
    print("   URL: http://localhost:5000/send-dm")
    print("   Method: POST")
    print("   Headers: Content-Type: application/json")
    print('   Body: {"username": "target_user", "message": "Hello!"}')
    print("\n🌐 Starting server on http://localhost:5000")
    
    try:
        # Test Instagram connection on startup
        get_instagram_client()
        print("✅ Instagram connection successful")
        
        # Start Flask server
        app.run(host='127.0.0.1', port=5000, debug=False)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
