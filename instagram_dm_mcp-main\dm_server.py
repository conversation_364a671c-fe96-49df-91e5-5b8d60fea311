#!/usr/bin/env python3
"""
Instagram DM API Server for n8n Cold Outreach Integration
"""

import os
import sys
from dotenv import load_dotenv
from instagrapi import Client
from flask import Flask, request, jsonify
from datetime import datetime
import logging

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global Instagram client
instagram_client = None

app = Flask(__name__)

def get_instagram_client():
    """Get or create Instagram client with persistent session"""
    global instagram_client
    
    if instagram_client is None:
        # Get credentials
        username = os.getenv("INSTAGRAM_USERNAME")
        password = os.getenv("INSTAGRAM_PASSWORD")
        
        if not username or not password:
            raise Exception("Instagram credentials not found in .env file")
        
        # Create and login
        instagram_client = Client()
        instagram_client.login(username, password)
        logger.info(f"✅ Logged into Instagram as {username}")
    
    return instagram_client

@app.route('/send-dm', methods=['POST'])
def send_dm():
    """
    Send Instagram DM
    Expected JSON: {"username": "target_user", "message": "your message"}
    """
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No JSON data provided"}), 400
        
        username = data.get('username')
        message = data.get('message')
        
        if not username or not message:
            return jsonify({
                "success": False, 
                "error": "Both 'username' and 'message' are required"
            }), 400
        
        logger.info(f"📤 Sending DM to {username}")
        
        # Get Instagram client
        client = get_instagram_client()
        
        # Get user ID
        user_id = client.user_id_from_username(username)
        if not user_id:
            return jsonify({
                "success": False,
                "error": f"User '{username}' not found"
            }), 404
        
        # Send message
        dm = client.direct_send(message, [user_id])
        
        if dm:
            result = {
                "success": True,
                "message": "DM sent successfully",
                "target_username": username,
                "target_user_id": str(user_id),
                "dm_id": str(getattr(dm, 'id', 'N/A')),
                "timestamp": datetime.now().isoformat()
            }
            logger.info(f"✅ DM sent to {username}")
            return jsonify(result), 200
        else:
            return jsonify({
                "success": False,
                "error": "Failed to send message"
            }), 500
    
    except Exception as e:
        logger.error(f"❌ Error: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/status', methods=['GET'])
def status():
    """Check server status"""
    try:
        client = get_instagram_client()
        # Test session
        client.account_info()
        
        return jsonify({
            "status": "healthy",
            "instagram_connected": True,
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "instagram_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        })

@app.route('/health', methods=['GET'])
def health():
    """Simple health check"""
    return jsonify({"status": "ok", "timestamp": datetime.now().isoformat()})

if __name__ == "__main__":
    print("🚀 Instagram DM Server for n8n Integration")
    print("=" * 45)
    print("🔗 Endpoints:")
    print("   POST /send-dm - Send Instagram DM")
    print("   GET  /status  - Check status")
    print("   GET  /health  - Health check")
    print("\n📝 Example n8n HTTP Request:")
    print("   URL: http://localhost:5000/send-dm")
    print("   Method: POST")
    print("   Headers: Content-Type: application/json")
    print('   Body: {"username": "target_user", "message": "Hello!"}')
    print("\n🌐 Starting server on http://localhost:5000")
    
    try:
        # Test Instagram connection on startup
        get_instagram_client()
        print("✅ Instagram connection successful")
        
        # Start Flask server
        app.run(host='127.0.0.1', port=5000, debug=False)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
