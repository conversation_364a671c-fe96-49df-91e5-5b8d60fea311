#!/usr/bin/env python3
"""
Test script for Instagram DM API Server
"""

import requests
import json
import time

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get('http://localhost:5000/health', timeout=10)
        print(f"Health check: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_status():
    """Test status endpoint"""
    try:
        response = requests.get('http://localhost:5000/status', timeout=10)
        print(f"Status check: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Status check failed: {e}")
        return False

def test_send_dm(username, message):
    """Test sending DM"""
    try:
        data = {
            "username": username,
            "message": message
        }
        
        response = requests.post(
            'http://localhost:5000/send-dm',
            headers={'Content-Type': 'application/json'},
            json=data,
            timeout=30
        )
        
        print(f"Send DM: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Send DM failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Instagram DM API Server")
    print("=" * 40)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Test health
    print("\n1. Testing health endpoint...")
    if test_health():
        print("✅ Health check passed")
    else:
        print("❌ Health check failed")
        exit(1)
    
    # Test status
    print("\n2. Testing status endpoint...")
    if test_status():
        print("✅ Status check passed")
    else:
        print("❌ Status check failed")
    
    # Test sending DM
    print("\n3. Testing send DM endpoint...")
    test_username = "Chirondalonda"
    test_message = "Hello from the API! This is a test message from your cold outreach automation system."
    
    if test_send_dm(test_username, test_message):
        print("✅ DM sent successfully")
    else:
        print("❌ DM sending failed")
    
    print("\n🎉 API testing completed!")
